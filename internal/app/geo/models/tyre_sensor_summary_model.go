package models

type TyreSensorSummary struct {
	TyrePosition                   int
	TyreSensorID                   string
	TyreSerialNumber               string
	TyreBrand                      string
	TyrePatternType                string
	TyreSize                       string
	TyrePlyRating                  string
	DistanceTravelledKM            float64
	NumberOfAlerts                 int
	AveragePressure                float64
	HighestPressure                float64
	LowestPressure                 float64
	AverageTemperature             float64
	HighestTemperature             float64
	LowestTemperature              float64
	AlertOverinflated              int
	AlertUnderInflated             int
	AlertHighTemperature           int
	AlertPressureMismatch          int
	TimeSpentOverinflatedHours     float64
	TimeSpentUnderInflatedHours    float64
	TimeSpentHighTemperatureHours  float64
	TimeSpentPressureMismatchHours float64
}

func (TyreSensorSummary) TableName() string {
	return "tyre_sensor_summary"
}
